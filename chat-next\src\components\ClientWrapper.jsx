"use client";

import React from 'react';
import Navbar from "./Navbar";
import { ChatProvider } from "../contexts/ChatContext";

/**
 * Client wrapper component that contains all client-side logic
 * This separates client components from server components to fix metadata issues
 * Following Next.js App Router best practices
 */
export default function ClientWrapper({ children }) {
  return (
    <ChatProvider>
      {/* <Navbar /> */}
      {children}
    </ChatProvider>
  );
}
