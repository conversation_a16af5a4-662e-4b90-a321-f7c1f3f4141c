"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { FiEdit } from "react-icons/fi";
import logo from "../assets/images/logo.png";
import { HiUserCircle } from "react-icons/hi2";
import { useChatContext } from "../contexts/ChatContext";

const Navbar = () => {
  const [businessName, setBusinessName] = useState("");

  // Get context values
  const { metadata, hasMetadata } = useChatContext();

  useEffect(() => {
    // First, try to get business name from context
    if (hasMetadata() && metadata.businessName) {
      setBusinessName(metadata.businessName);
      return;
    }

    // Fallback to localStorage and event listeners
    const checkBusinessName = () => {
      const storedName = localStorage.getItem("BusinessName") || "Driply";
      if (storedName && storedName !== "undefined" && storedName !== "null") {
        setBusinessName(storedName);
        return true;
      }
      return false;
    };

    if (checkBusinessName()) {
      return;
    }

    const handleBusinessNameLoaded = (event) => {
      if (event.detail && event.detail.businessName) {
        setBusinessName(event.detail.businessName);
      }
    };

    window.addEventListener("businessNameLoaded", handleBusinessNameLoaded);

    const interval = setInterval(() => {
      if (checkBusinessName()) {
        clearInterval(interval);
      }
    }, 100);

    const timeout = setTimeout(() => {
      clearInterval(interval);
    }, 5000);

    return () => {
      window.removeEventListener(
        "businessNameLoaded",
        handleBusinessNameLoaded
      );
      clearInterval(interval);
      clearTimeout(timeout);
    };
  }, [metadata, hasMetadata]);

  const handleNewConversation = () => {
    localStorage.removeItem("userID");
    localStorage.removeItem("customerName_userId");
    localStorage.removeItem("BusinessName");
    localStorage.removeItem("userId");
    window.location.reload();
  };

  const handleHomeClick = (e) => {
    e.preventDefault();
    window.location.reload();
  };

  return (
    <nav className=" bg-white fixed top-0 left-0 right-0 z-50">
      <div className="flex justify-between items-center h-15 px-4">
        <div className="flex items-center gap-2 ">
          {metadata.image ? (
            <>
              <Image
                src={metadata.image}
                alt="user-image"
                className="h-10 w-10 hover:border hover:border-gray-400 rounded-full"
              />
            </>
          ) : (
            <HiUserCircle className="text-black text-xl w-10 h-10 rounded-full object-cover hover:border hover:border-gray-400" />
          )}
          <Link href="/" onClick={handleHomeClick}>
            <span className="text-[17px] font-[500] text-black ">
              {businessName}
            </span>
          </Link>
        </div>
        <button
          onClick={handleNewConversation}
          className="px-4 py-2 rounded-lg cursor-pointer"
          title="Start New Conversation"
        >
          <FiEdit className="text-black text-xl" />
        </button>
      </div>
    </nav>
  );
};

export default Navbar;
